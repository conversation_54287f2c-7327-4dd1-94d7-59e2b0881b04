import { IncomingMessage } from "http";
import * as Errors from "../errors";
import * as request from "request";
import { measures, sleep } from "@skywind-group/sw-utils";
import { generateInternalToken } from "../utils/token";
import logger from "../utils/logger";
import { BaseEntity, EntityWithChild, MIGRATION_STATUS } from "../entities/entity";
import { buildDynamicGSUrlByDomain } from "./entityDomainService";
import { Models } from "../models/models";
import { findOne } from "./entity";
import EntityCache from "../cache/entity";
import config from "../config";
import { getDomainService } from "./domain";
import { getGameServerApiProvider } from "./deploymentGroup";
import { BrandEntity } from "../entities/brand";
import measure = measures.measure;
import { DynamicDomain } from "../entities/domain";
import { Transaction } from "sequelize";

const log = logger("force-cleanup");
const FORCE_CLEANUP_ENDPOINT = "force-cleanup";
const FORCE_CLEANUP_PLAYER_ENDPOINT = "force-cleanup/interrupt-player";
const REACTIVATE_ENDPOINT = "force-cleanup/reactivate-game";

const EntityModel = Models.EntityModel;
const DynamicDomainModel = Models.DynamicDomainModel;

class MigrationService {

    @measure({ name: "MigrationService.startMigration" })
    public async startMigration(brandId: number, prevDomains: DynamicDomain[], transaction?: Transaction): Promise<void> {
        return measures.measureProvider.runInTransaction("Merchant/brand migration", async () => {
            log.info("Starting force cleanup job for brand id = %s ", brandId);
            let attempts = config.migration.forceCleanupAttempts;
            do {
                attempts--;
                try {
                    for (const prevDomain of prevDomains) {
                        const url = await buildDynamicGSUrlByDomain(prevDomain, FORCE_CLEANUP_ENDPOINT);
                        await this.delete(url, { brandId });
                    }
                    await EntityModel.update(
                        {
                            migrationStatus: MIGRATION_STATUS.PROCESSING
                        },
                        {
                            where: {
                                id: brandId,
                                migrationStatus: MIGRATION_STATUS.STARTED
                            },
                            transaction
                        }
                    );
                    break;
                } catch (err) {
                    log.error(err, "Error when trying to send force cleanup for brand id %s", brandId);
                    if (attempts > 0) {
                        await sleep(1000);
                    } else {
                        return Promise.reject(err);
                    }
                }
            } while (attempts > 0);
        });
    }

    public checkStuckMigrations() {
        if (config.migration.checkStuckMigrationInternal) {
            setInterval(() => this.doCheckMigrations(), config.migration.checkStuckMigrationInternal);
        }
    }

    public async doCheckMigrations() {
        log.info("Checking migrations jobs");
        const master = await findOne({});
        const check = async (parent: BaseEntity | EntityWithChild) => {
            if (parent.isBrand() && !!parent.prevDynamicDomainId &&
                parent.migrationStatus !== MIGRATION_STATUS.PROCESSING) {
                log.info("Found on-going migration for entity key %s", parent.key);
                const dynamicDomain: DynamicDomain =
                    await DynamicDomainModel.findByPk(parent.prevDynamicDomainId);
                setImmediate(() => this.startMigration(parent.id, [dynamicDomain]));
            } else {
                const entity = parent as EntityWithChild;
                if (entity.child) {
                    await Promise.all(entity.child.map(item => check(item)));
                }
            }
        };
        await check(master);
    }

    public async markMigrationFinished(brandId: number): Promise<void> {
        const brand = await findOne({ id: brandId });
        if (!!brand.prevDynamicDomainId) {
            const [updatedRows] = await EntityModel.update({
                prevDynamicDomainId: null,
                migrationStatus: null
            } as any, { where: { id: brand.id, prevDynamicDomainId: brand.prevDynamicDomainId } });

            if (updatedRows !== 1) {
                return Promise.reject(new Errors.CannotMarkMigrationFinishedError());
            }

            brand.prevDynamicDomainId = undefined;
            EntityCache.reset();
        }
    }

    @measure({ name: "MigrationService.forceMigratePlayer" })
    public async forceMigratePlayer(brand: BaseEntity, playerCode: string): Promise<any> {
        if (brand.prevDynamicDomainId) {
            const domain: DynamicDomain = await getDomainService().findOne(brand.prevDynamicDomainId) as DynamicDomain;
            const url = await buildDynamicGSUrlByDomain(domain, FORCE_CLEANUP_PLAYER_ENDPOINT);
            return this.delete(url, { brandId: brand.id, playerCode });
        }
    }

    @measure({ name: "MigrationService.reactivateGame" })
    public async reactivateGame(brand: BrandEntity, gameContextId: string): Promise<any> {
        const token = await generateInternalToken({ gameContextId });
        return getGameServerApiProvider().sendPostByEntity<void>(REACTIVATE_ENDPOINT, { token }, brand);
    }

    private async delete<T>(url: string, req): Promise<T> {
        log.info("Sending  to %s , request %j", url, req);
        const token = await generateInternalToken(req);
        return new Promise<T>((resolve, reject) => {
            request.delete(url, {
                headers: { "Content-Type": "application/json" },
                body: { token },
                json: true
            }, this.processResponse(resolve, reject));
        });
    }

    private processResponse(resolve, reject): (error: any, response: IncomingMessage, body: any) => Promise<any> {
        return function(error: Error, response: IncomingMessage, body: any): Promise<any> {
            if (error) {
                log.error(error, "Failed to query game server");
                return reject(new Errors.ErrorQueryingGameServer());
            } else if (response.statusCode !== 200 && response.statusCode !== 201) {
                log.error({ body, statusCode: response.statusCode }, "Failed to send request to game server");
                return reject(new Errors.ErrorQueryingGameServer(body));
            } else {
                return resolve(body);
            }
        };
    }
}

export default new MigrationService();
