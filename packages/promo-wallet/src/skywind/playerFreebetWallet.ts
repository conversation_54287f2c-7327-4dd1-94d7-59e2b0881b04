import { logging } from "@skywind-group/sw-utils";
import { IAccount, IIncrementalChange, ITrxData, IWallet } from "@skywind-group/sw-wallet";
import {
    AccountPropertiesFilter,
    getAsyncLocalWalletStore,
    OPERATION_ID,
    PLAYER,
    PlayerBalanceProvider,
    runWithAsyncLocalWallet,
    WALLET_TRX_TYPE,
    WalletFacade
} from "@skywind-group/sw-management-wallet";
import { FreeBetInfo } from "@skywind-group/sw-wallet-adapter-core";
import { Currencies, Currency } from "@skywind-group/sw-currency-exchange";
import { PromoWalletErrors } from "./errors";
import { WALLET_LAST_FREE_BET_ROUND_PENDING_PROPERTY, WALLET_PLAY_PROPERTY } from "./constants";
import { config } from "./config";

const log = logging.logger("sw-management-promo-wallet:free-bets");

interface CommonFreebetsData {
    startDate: number;
    endDate?: number;
    amount: number;
    isPendingRoundEnd?: boolean;
}

export interface FreebetsData extends CommonFreebetsData {
    promoId: number;
    rewardId: number;
}

export interface BonusFreebetsData extends CommonFreebetsData {
    promoId: string;
    rewardId: string;
    isIndividualBonus: boolean;
    coin: number;
    gameCode: string;
    currency: string;
    externalId?: string;
}

export interface PlayerGameFreebet {
    promoId: number | string;
    rewardId: number | string;
    coin: number;
    coinMultiplier?: number;
    externalId?: string;
    isIndividualBonus?: boolean;
}

export interface FreeBetsAccountFilter extends AccountPropertiesFilter {
    rewards: PlayerGameFreebet[];
}

export interface PlayerFreeBetBalance {
    freeBets?: FreeBetsBalance;
}

export interface FreeBetsBalance {
    amount: number;
    // EGP can have activePromoId string
    activePromoId?: number | string;
    externalId?: string;
}

export class PlayerFreebetWallet implements PlayerBalanceProvider<PlayerFreeBetBalance> {
    private readonly walletKey: string;
    private readonly account: IAccount;
    private readonly walletCurrency: Currency;
    private readonly freeBetsData: (FreebetsData | BonusFreebetsData)[];
    private expiredFreeBetsData: (FreebetsData | BonusFreebetsData)[];

    constructor(private wallet: IWallet, public readonly currency: string, skipCommittingExpired?: boolean) {
        this.walletKey = wallet.key;
        this.account = wallet.accounts.get(PLAYER.PLAYER_FREE_BETS_ACCOUNT);
        this.freeBetsData = this.parseFreebets(this.account, skipCommittingExpired);
        this.walletCurrency = Currencies.get(this.currency);
    }

    public async getPlayerBalance(filter: FreeBetsAccountFilter): Promise<PlayerFreeBetBalance | undefined> {
        if (filter.account === PLAYER.PLAYER_FREE_BETS_ACCOUNT) {
            const freeBets = this.getFreebetsBalance(filter.rewards);
            if (freeBets) {
                return { freeBets };
            }
        }
    }

    public get playerWallet(): IWallet {
        return this.wallet;
    }

    public get freeBets(): ReadonlyArray<FreebetsData | BonusFreebetsData> {
        return Object.freeze(this.freeBetsData);
    }

    public getFreeBets(): (FreebetsData | BonusFreebetsData)[] {
        return this.freeBetsData;
    }

    public hasFreebetPromo(promoId: number | string): boolean {
        for (const freeBet of this.freeBetsData) {
            if (freeBet.promoId === promoId) {
                return true;
            }
        }
        return false;
    }

    public getFreeBetBonus(gameCode: string, currency: string): BonusFreebetsData | undefined {
        const bonusFreeBetsData = this.freeBetsData as BonusFreebetsData[];
        return bonusFreeBetsData.find(item => {
            return item.isIndividualBonus && item.gameCode === gameCode && item.currency === currency;
        });
    }

    public getFreebetPromoData(promoId: number | string, rewardId: number | string): FreebetsData | BonusFreebetsData {
        for (const freeBet of this.freeBetsData) {
            if (freeBet.promoId === promoId && freeBet.rewardId === rewardId) {
                return freeBet;
            }
        }
        return null;
    }

    public addFreebets(freeBet: FreebetsData | BonusFreebetsData): IIncrementalChange {
        const prop = this.getBonusPropertySuffix(freeBet as BonusFreebetsData);
        const property = this.getFreebetProperty(freeBet, prop);
        const changes = this.account.inc(property, freeBet.amount, WALLET_TRX_TYPE.FREE_BET, 0);
        this.freeBetsData.push(freeBet);
        return changes;
    }

    public resetFreebets(freeBet: FreebetsData | BonusFreebetsData) {
        const freebetData = this.getFreebetPromoData(freeBet.promoId, freeBet.rewardId);
        const prop = this.getBonusPropertySuffix(freeBet as BonusFreebetsData);
        const property = this.getFreebetProperty(freebetData, prop);
        this.account.inc(property, -freebetData.amount, WALLET_TRX_TYPE.FREE_BET_RESET, 0);
        freebetData.amount = 0;
    }

    public async playFreebet(filters: FreeBetsAccountFilter | AccountPropertiesFilter[],
                             bet: number,
                             coin: number,
                             gameCode?: string): Promise<void> {
        const filter = this.findFilter(filters);
        let playingFreeBet: FreebetsData | BonusFreebetsData;
        if (filter) {
            for (const freeBet of this.freeBetsData) {
                if (this.isActive(freeBet)) {
                    const reward = this.findReward(filter.rewards, freeBet, coin);
                    if (reward) {
                        playingFreeBet = freeBet;
                        break;
                    }
                }
            }
        }
        if (!playingFreeBet) {
            return Promise.reject(new PromoWalletErrors.InsufficientFreebet());
        }
        const prop = this.getBonusPropertySuffix(playingFreeBet as BonusFreebetsData);
        const property = this.getFreebetProperty(playingFreeBet, prop);
        playingFreeBet.amount -= 1;
        this.account.inc(property, -1, WALLET_TRX_TYPE.FREE_BET, 0);
        const mainAccount = this.wallet.accounts.get(PLAYER.PLAYER_MAIN_ACCOUNT);
        mainAccount.inc(PLAYER.PLAYER_TOTAL_FREE_BET_AMOUNT, this.walletCurrency.toMinorUnits(bet),
            WALLET_TRX_TYPE.TOTAL_FREE_BET, 0);

        if (playingFreeBet.amount === 0) {
            const playProperty = this.getFreebetProperty(playingFreeBet,
                prop ? `${prop}/${WALLET_PLAY_PROPERTY}` : WALLET_PLAY_PROPERTY);
            this.account.set(playProperty, 1, WALLET_TRX_TYPE.FREE_BET_PLAYED, false);

            const pendingProperty = this.getFreebetProperty(
                playingFreeBet,
                prop ?
                `${prop}/${WALLET_LAST_FREE_BET_ROUND_PENDING_PROPERTY}` :
                WALLET_LAST_FREE_BET_ROUND_PENDING_PROPERTY
            );
            // Using increment so that the lua script actually updates this flag in Redis
            this.account.inc(pendingProperty, 1, WALLET_TRX_TYPE.LAST_FREE_BET_ROUND_PENDING, 0);
        }
    }

    private findFilter(filters: FreeBetsAccountFilter | AccountPropertiesFilter[]) {
        return (Array.isArray(filters) ?
                filters.find(item => item.account === PLAYER.PLAYER_FREE_BETS_ACCOUNT) :
                filters) as FreeBetsAccountFilter;
    }

    public async reduceTotalFreeBetAmount(currency: string): Promise<number> {
        const account: IAccount = this.wallet.accounts.get(PLAYER.PLAYER_MAIN_ACCOUNT);
        const walletCurrency = Currencies.get(currency);
        const amount = account.get(PLAYER.PLAYER_TOTAL_FREE_BET_AMOUNT) as number;

        if (amount) {
            account.inc(PLAYER.PLAYER_TOTAL_FREE_BET_AMOUNT,
                -amount,
                WALLET_TRX_TYPE.TOTAL_FREE_BET,
                0);
        }

        return amount ? walletCurrency.toMajorUnits(amount) : 0;
    }

    private findReward(rewards: PlayerGameFreebet[],
                       freeBetData: FreebetsData | BonusFreebetsData,
                       coin: number): PlayerGameFreebet {
        return rewards.find(r =>
            r.promoId === freeBetData.promoId && r.rewardId === freeBetData.rewardId && r.coin === coin);
    }

    public async rollbackFreebet(transaction: ITrxData): Promise<void> {
        for (const change of transaction.data) {
            if (change.trxType === WALLET_TRX_TYPE.FREE_BET) {
                for (const freebet of this.freeBetsData) {
                    const prop = this.getBonusPropertySuffix(freebet as BonusFreebetsData);
                    if (change.property === this.getFreebetProperty(freebet, prop)) {
                        freebet.amount += 1;
                        this.account.inc(change.property, 1, WALLET_TRX_TYPE.FREE_BET, 0);
                    }
                }
            }
        }
    }

    public getFreebetsBalance(rewards: PlayerGameFreebet[], emulateFreeBetBalance: boolean = false): FreeBetsBalance {
        let freeBetsAmount = 0;
        let activePromoId;
        let externalId: string;
        const theEarliestFreebetData = {
            firstFreebet: undefined,
            firstFreebetReward: undefined
        };

        for (const reward of rewards) {
            const freeBet = this.freeBetsData.find(
                item => item.promoId === reward.promoId && item.rewardId === reward.rewardId);
            if (freeBet && this.isActive(freeBet)) {
                freeBetsAmount += freeBet.amount;
                if (this.isEarlierActiveFreebet(freeBet, reward, theEarliestFreebetData)) {
                    activePromoId = reward.promoId;
                    externalId = reward.externalId;
                }
            } else if (emulateFreeBetBalance) {
                // we should emulate balance for last freebet for splitPayment WIN
                activePromoId = reward.promoId;
                externalId = reward.externalId;
            }
        }

        if (!activePromoId) {
            return;
        }

        const result: FreeBetsBalance = {
            amount: freeBetsAmount,
            activePromoId,
        };

        if (externalId) {
            result.externalId = externalId;
        }

        return result;
    }

    public async closeLastFreeBetRound(filters: AccountPropertiesFilter[]): Promise<void> {
        const filter = this.findFilter(filters);
        let playingFreeBet: FreebetsData | BonusFreebetsData;
        if (filter) {
            for (const freeBet of this.freeBetsData) {
                if (this.isActive(freeBet)) {
                    const reward = filter.rewards.find(r =>
                        r.promoId === freeBet.promoId && r.rewardId === freeBet.rewardId
                    );
                    if (reward) {
                        playingFreeBet = freeBet;
                        break;
                    }
                }
            }
        }
        if (!playingFreeBet) {
            return;
        }
        const prop = this.getBonusPropertySuffix(playingFreeBet as BonusFreebetsData);
        const pendingProperty = this.getFreebetProperty(
            playingFreeBet,
            prop ?
            `${prop}/${WALLET_LAST_FREE_BET_ROUND_PENDING_PROPERTY}` :
            WALLET_LAST_FREE_BET_ROUND_PENDING_PROPERTY
        );
        // Using increment so that the lua script actually updates this flag in Redis
        this.account.inc(pendingProperty, -1, WALLET_TRX_TYPE.LAST_FREE_BET_ROUND_CLOSE, 0);
    }

    public getFreebetsInfo(gameCode: string, stakeAll: number[], rewards: PlayerGameFreebet[],
                           skipCoinValidation?: boolean): FreeBetInfo {
        if (!rewards || !rewards.length) {
            throw new PromoWalletErrors.InsufficientFreebet();
        }

        const freebetInfo: FreeBetInfo = {
            amount: 0,
            coin: 0
        };
        const theEarliestFreebetData = {
            firstFreebet: undefined,
            firstFreebetReward: undefined
        };
        let hasActiveFreebets = false;

        for (const reward of rewards) {
            const freebetData = this.freeBetsData.find(
                item => item.promoId === reward.promoId && item.rewardId === reward.rewardId);
            if (freebetData && this.isActive(freebetData)) {
                hasActiveFreebets = true;

                if (!skipCoinValidation && stakeAll.indexOf(reward.coin) === -1) {
                    continue;
                }
                // TODO amount accumulated for different coins
                freebetInfo.amount += freebetData.amount;
                // select the earliest promo reward if there are multiple free bets
                if (this.isEarlierActiveFreebet(freebetData, reward, theEarliestFreebetData)) {
                    freebetInfo.coin = reward.coin;
                }
            }
        }

        if (hasActiveFreebets && freebetInfo.coin === 0) {
            throw new PromoWalletErrors.InvalidFreebet();
        }

        if (freebetInfo.amount === 0) {
            throw new PromoWalletErrors.InsufficientFreebet();
        }

        return freebetInfo;
    }

    private isEarlierActiveFreebet(freebetWalletData: FreebetsData | BonusFreebetsData,
                                   reward: PlayerGameFreebet,
                                   theEarliestFreebetData): boolean {
        if (!theEarliestFreebetData.firstFreebet ||
            (freebetWalletData.startDate < theEarliestFreebetData.firstFreebet.startDate) ||
            (freebetWalletData.startDate === theEarliestFreebetData.firstFreebet.startDate &&
                reward.rewardId < theEarliestFreebetData.firstFreebetReward.rewardId)) {
            theEarliestFreebetData.firstFreebet = freebetWalletData;
            theEarliestFreebetData.firstFreebetReward = reward;
            return true;
        }
        return false;
    }

    private getFreebetProperty(freeBet: FreebetsData | BonusFreebetsData, prop: string): string {
        const propertyName = `promo/${freeBet.promoId}/${freeBet.rewardId}/${freeBet.startDate}/${freeBet.endDate || ""}`;
        if (prop) {
            return `${propertyName}/${prop}`;
        }

        return propertyName;
    }

    private getBonusPropertySuffix(freeBet: BonusFreebetsData): string | undefined {
        const { gameCode, currency, coin, externalId } = freeBet;
        if (!gameCode || !currency || !coin) {
            return;
        }
        return externalId ? `${gameCode}/${currency}/${coin}/${externalId}` : `${gameCode}/${currency}/${coin}`;
    }

    private parseFreebets(account: IAccount, skipCommittingExpired?: boolean): (FreebetsData | BonusFreebetsData)[] {
        const now = Date.now();
        const result: (FreebetsData | BonusFreebetsData)[] = [];
        const expired: (FreebetsData | BonusFreebetsData)[] = [];
        // TODO get property names and iterate over them
        for (const property of Object.keys(account.data)) {
            const data = property.split("/");
            if (data[0] !== "promo") {
                continue;
            }
            const isPendingRoundEnd = data[data.length - 1] === WALLET_LAST_FREE_BET_ROUND_PENDING_PROPERTY;
            const promoId = !isNaN(+data[1]) ? +data[1] : data[1];
            const rewardId = !isNaN(+data[2]) ? +data[2] : data[2];
            const startDate = +data[3];
            const endDate = data[4] ? +data[4] : undefined;
            const gameCode = data[5];
            const currency = data[6];
            const coin = !isNaN(+data[7]) ? +data[7] : undefined;
            const externalId = data[8];
            const amount = isPendingRoundEnd ? 0 : +account.get(property);
            const isIndividualBonus = typeof promoId === "string";
            const freeBet: FreebetsData | BonusFreebetsData = {
                promoId,
                rewardId,
                startDate,
                endDate,
                amount,
                isIndividualBonus,
                isPendingRoundEnd
            } as any;
            if (data[5] !== WALLET_LAST_FREE_BET_ROUND_PENDING_PROPERTY) {
                if (gameCode) {
                    (freeBet as BonusFreebetsData).gameCode = gameCode;
                }
                if (currency) {
                    (freeBet as BonusFreebetsData).currency = currency;
                }
                if (coin) {
                    (freeBet as BonusFreebetsData).coin = coin;
                }
                if (externalId && externalId !== WALLET_LAST_FREE_BET_ROUND_PENDING_PROPERTY) {
                    (freeBet as BonusFreebetsData).externalId = externalId;
                }
            }

            if (endDate > now && (amount > 0 || isPendingRoundEnd)) {
                result.push(freeBet);
            } else {
                expired.push(freeBet);
            }
        }
        if (skipCommittingExpired) {
            this.expiredFreeBetsData = expired;
            return result;
        }
        if (expired.length) {
            if (config.asyncLocalWalletEnabled) {
                const store = getAsyncLocalWalletStore();
                runWithAsyncLocalWallet(store, async () => {
                    try {
                        await this.cleanExpired(expired);
                    } catch (err) {
                        log.warn(err, "Failed to clean up expired free bets", expired);
                    }
                });
            } else {
                this.cleanExpired(expired).catch(err => {
                    log.warn(err, "Failed to clean up expired free bets", expired);
                });
            }
        }

        return result;
    }

    public async cleanPendingExpiredFreeBets(): Promise<void> {
        if (this.expiredFreeBetsData.length) {
            try {
                await this.cleanExpired(this.expiredFreeBetsData);
                this.expiredFreeBetsData = [];
            } catch (err) {
                log.warn(err, "Failed to clean up pending expired free bets", this.expiredFreeBetsData);
            }
        }
    }

    private async cleanExpired(freeBets: (FreebetsData | BonusFreebetsData)[]): Promise<void> {
        const trxId = await WalletFacade.generateTransactionId();
        const transaction = await WalletFacade.startTransactionWithID(trxId, {
            operationId: OPERATION_ID.FREE_BET_EXPIRE,
            operationName: "free-bet-expire"
        });
        const playerWallet = config.asyncLocalWalletEnabled ?
                             this.wallet :
                             await transaction.getWallet(this.walletKey);

        if (config.asyncLocalWalletEnabled) {
            transaction.setWallet(playerWallet.key, playerWallet);
            transaction.changes = playerWallet.changes;
        }

        const account = playerWallet.accounts.get(PLAYER.PLAYER_FREE_BETS_ACCOUNT);
        const propertiesToDelete = [];
        for (const item of freeBets) {
            const propSuffix = this.getBonusPropertySuffix(item as BonusFreebetsData);
            const prop = item.isPendingRoundEnd ?
                         (propSuffix ?
                          `${propSuffix}/${WALLET_LAST_FREE_BET_ROUND_PENDING_PROPERTY}` :
                          WALLET_LAST_FREE_BET_ROUND_PENDING_PROPERTY) :
                         propSuffix;
            const property = this.getFreebetProperty(item, prop)
            propertiesToDelete.push(property);
            this.cleanExpiredFreebet(account, property, item.isPendingRoundEnd ? 1 : item.amount);
        }
        try {
            await transaction.commit();
            await this.cleanAsyncLocalWallet(account, propertiesToDelete);
        } catch (err) {
            // If we get INSUFFICIENT_BALANCE on expiration, we can skip this error
            if (err === "INSUFFICIENT_BALANCE") {
                await this.cleanAsyncLocalWallet(account, propertiesToDelete);
            }
            throw err;
        }
    }

    private async cleanAsyncLocalWallet(account: IAccount, propertiesToDelete: string[]) {
        if (config.asyncLocalWalletEnabled) {
            for (const property of propertiesToDelete) {
                delete this.account.data[property];
                delete account.data[property];
                const wallet = getAsyncLocalWalletStore()?.wallet;
                if (wallet) {
                    delete wallet[`${PLAYER.PLAYER_FREE_BETS_ACCOUNT}:${property}`];
                }
                this.wallet = await WalletFacade.create(this.walletKey, wallet);
            }
        }
    }

    private cleanExpiredFreebet(account: IAccount, property: string, amount: number) {
        // zero value property is cleaned up by wallet automatically
        account.inc(property,
            -amount,
            WALLET_TRX_TYPE.FREE_BET_EXPIRE);
    }

    public isActive(freeBet: FreebetsData | BonusFreebetsData) {
        const now = Date.now();
        return freeBet.startDate <= now && (!freeBet.endDate || freeBet.endDate > now);
    }
}
